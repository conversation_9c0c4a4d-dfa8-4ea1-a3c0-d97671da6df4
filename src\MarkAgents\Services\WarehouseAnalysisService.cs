using KoalaWiki.Core.DataAccess;
using MarkAgents.DataAccess;
using MarkAgents.Dtos;
using MarkAgents.Entities;
using Microsoft.EntityFrameworkCore;

namespace MarkAgents.Services;

public class WarehouseAnalysisService : IWarehouseAnalysisService
{
    private readonly MarkDbContext _context;
    private readonly IKoalaWikiContext _koalaWikiContext;

    public WarehouseAnalysisService(MarkDbContext context, IKoalaWikiContext koalaWikiContext)
    {
        _context = context;
        _koalaWikiContext = koalaWikiContext;
    }

    public async Task<List<WarehouseAnalysisDto>> GetAllAnalysesAsync()
    {
        // 首先获取基本的分析数据
        var analyses = await _context.WarehouseAnalyses
            .OrderByDescending(w => w.CreatedAt)
            .ToListAsync();

        var result = new List<WarehouseAnalysisDto>();
        
        foreach (var analysis in analyses)
        {
            var dto = await MapToWarehouseAnalysisDtoAsync(analysis);
            result.Add(dto);
        }

        return result;
    }

    private async Task<WarehouseAnalysisDto> MapToWarehouseAnalysisDtoAsync(WarehouseAnalysis analysis)
    {
        // 获取相关的任务
        var tasks = await _context.AnalysisAgentsTasks
            .Where(t => t.WarehouseAnalysisId == analysis.Id)
            .ToListAsync();

        var taskDtos = new List<AgentTaskDto>();

        foreach (var task in tasks)
        {
            var taskDto = await MapToAgentTaskDtoAsync(task);
            taskDtos.Add(taskDto);
        }

        // 获取仓库信息
        var warehouse = await _koalaWikiContext.Warehouses
            .FirstOrDefaultAsync(w => w.Id == analysis.WarehouseId);

        WarehouseInfoDto? warehouseInfo = null;
        if (warehouse != null)
        {
            warehouseInfo = new WarehouseInfoDto
            {
                Id = warehouse.Id,
                OrganizationName = warehouse.OrganizationName,
                Name = warehouse.Name,
                Description = warehouse.Description,
                Address = warehouse.Address,
                Type = warehouse.Type,
                Branch = warehouse.Branch,
                Status = warehouse.Status.ToString(),
                CreatedAt = warehouse.CreatedAt,
                UpdatedAt = warehouse.CreatedAt // 使用CreatedAt作为UpdatedAt
            };
        }

        return new WarehouseAnalysisDto
        {
            Id = analysis.Id,
            WarehouseId = analysis.WarehouseId,
            StartCommitId = analysis.StartCommitId,
            CommitId = analysis.CommitId,
            Topic = analysis.Topic,
            Status = analysis.Status,
            Summary = analysis.Summary,
            CompletedAt = analysis.CompletedAt,
            CreatedAt = analysis.CreatedAt,
            AnalysisAgentsTasks = taskDtos,
            WarehouseInfo = warehouseInfo
        };
    }

    private async Task<AgentTaskDto> MapToAgentTaskDtoAsync(AnalysisAgentsTask task)
    {
        // 获取任务项
        var items = await _context.AnalysisAgentsTaskItems
            .Where(i => i.AnalysisAgentsTaskId == task.Id)
            .ToListAsync();

        var itemDtos = new List<AgentTaskItemDto>();

        foreach (var item in items)
        {
            var itemDto = MapToAgentTaskItemDto(item);
            itemDtos.Add(itemDto);
        }

        return new AgentTaskDto
        {
            Id = task.Id,
            WarehouseId = task.WarehouseId,
            AgentName = task.AgentName,
            Name = task.Name,
            Description = task.Description,
            CreatedAt = task.CreatedAt,
            StartedAt = task.StartedAt,
            CompletedAt = task.CompletedAt,
            Status = task.Status,
            Error = task.Error,
            Progress = task.Progress,
            WarehouseAnalysisId = task.WarehouseAnalysisId,
            AgentResult = task.AgentResult,
            Metadata = task.Metadata,
            Items = itemDtos
        };
    }

    private AgentTaskItemDto MapToAgentTaskItemDto(AnalysisAgentsTaskItems item)
    {
        // 映射文件信息
        var fileDtos = new List<AgentTaskItemFileDto>();

        foreach (var file in item.Files)
        {
            var fileDto = MapToAgentTaskItemFileDto(file);
            fileDtos.Add(fileDto);
        }

        return new AgentTaskItemDto
        {
            Id = item.Id,
            AnalysisAgentsTaskId = item.AnalysisAgentsTaskId,
            Log = item.Log,
            Status = item.Status,
            StartedAt = item.StartedAt,
            CompletedAt = item.CompletedAt,
            Summary = item.Summary,
            TotalFiles = item.TotalFiles,
            ProcessedFiles = item.ProcessedFiles,
            IssuesFound = item.IssuesFound,
            MaxSeverity = item.MaxSeverity,
            Files = fileDtos
        };
    }

    private AgentTaskItemFileDto MapToAgentTaskItemFileDto(AnalysisAgentsTaskItemsFile file)
    {
        // 映射代码文件分析结果
        var analysisResults = file.CodeFileAnalysisResults.Select(r => new CodeFileAnalysisResultDto
        {
            Offset = r.Offset,
            Limit = r.Limit,
            Report = r.Report
        }).ToList();

        return new AgentTaskItemFileDto
        {
            FilePath = file.FilePath,
            Severity = file.Severity,
            Report = file.Report,
            CodeFileAnalysisResults = analysisResults
        };
    }

    public async Task<WarehouseAnalysisDto?> GetAnalysisByIdAsync(long id)
    {
        var analysis = await _context.WarehouseAnalyses
            .FirstOrDefaultAsync(w => w.Id == id);

        if (analysis == null)
            return null;

        return await MapToWarehouseAnalysisDtoAsync(analysis);
    }

    public async Task<List<WarehouseAnalysisDto>> GetAnalysesByWarehouseIdAsync(string warehouseId)
    {
        var analyses = await _context.WarehouseAnalyses
            .Where(w => w.WarehouseId == warehouseId)
            .OrderByDescending(w => w.CreatedAt)
            .ToListAsync();

        var result = new List<WarehouseAnalysisDto>();
        
        foreach (var analysis in analyses)
        {
            var dto = await MapToWarehouseAnalysisDtoAsync(analysis);
            result.Add(dto);
        }

        return result;
    }

    public async Task<List<WarehouseAnalysisDto>> GetAnalysesByStatusAsync(AnalysisStatus status)
    {
        var analyses = await _context.WarehouseAnalyses
            .Where(w => w.Status == status)
            .OrderByDescending(w => w.CreatedAt)
            .ToListAsync();

        var result = new List<WarehouseAnalysisDto>();
        
        foreach (var analysis in analyses)
        {
            var dto = await MapToWarehouseAnalysisDtoAsync(analysis);
            result.Add(dto);
        }

        return result;
    }

    public async Task<WarehouseAnalysisDto> CreateAnalysisAsync(CreateWarehouseAnalysisDto dto)
    {
        var analysis = new WarehouseAnalysis
        {
            WarehouseId = dto.WarehouseId,
            StartCommitId = dto.StartCommitId,
            CommitId = dto.CommitId,
            Topic = dto.Topic,
            Status = AnalysisStatus.Pending,
            CreatedAt = DateTime.UtcNow
        };

        _context.WarehouseAnalyses.Add(analysis);
        await _context.SaveChangesAsync();

        return new WarehouseAnalysisDto
        {
            Id = analysis.Id,
            WarehouseId = analysis.WarehouseId,
            StartCommitId = analysis.StartCommitId,
            CommitId = analysis.CommitId,
            Topic = analysis.Topic,
            Status = analysis.Status,
            Summary = analysis.Summary,
            CompletedAt = analysis.CompletedAt,
            CreatedAt = analysis.CreatedAt,
            AnalysisAgentsTasks = new List<AgentTaskDto>()
        };
    }

    public async Task<WarehouseAnalysisDto?> UpdateAnalysisAsync(long id, UpdateWarehouseAnalysisDto dto)
    {
        var analysis = await _context.WarehouseAnalyses.FindAsync(id);
        if (analysis == null)
            return null;

        if (!string.IsNullOrEmpty(dto.Topic))
            analysis.Topic = dto.Topic;

        if (!string.IsNullOrEmpty(dto.Summary))
            analysis.Summary = dto.Summary;

        if (dto.Status.HasValue)
        {
            analysis.Status = dto.Status.Value;
            if (dto.Status.Value == AnalysisStatus.Completed)
                analysis.CompletedAt = DateTime.UtcNow;
        }

        await _context.SaveChangesAsync();

        return new WarehouseAnalysisDto
        {
            Id = analysis.Id,
            WarehouseId = analysis.WarehouseId,
            StartCommitId = analysis.StartCommitId,
            CommitId = analysis.CommitId,
            Topic = analysis.Topic,
            Status = analysis.Status,
            Summary = analysis.Summary,
            CompletedAt = analysis.CompletedAt,
            CreatedAt = analysis.CreatedAt,
            AnalysisAgentsTasks = new List<AgentTaskDto>()
        };
    }

    public async Task<bool> DeleteAnalysisAsync(long id)
    {
        var analysis = await _context.WarehouseAnalyses
            .Include(w => w.AnalysisAgentsTasks)
            .ThenInclude(t => t.Items)
            .FirstOrDefaultAsync(w => w.Id == id);

        if (analysis == null)
            return false;

        // Remove related tasks and items
        foreach (var task in analysis.AnalysisAgentsTasks)
        {
            _context.AnalysisAgentsTaskItems.RemoveRange(task.Items);
        }
        _context.AnalysisAgentsTasks.RemoveRange(analysis.AnalysisAgentsTasks);
        _context.WarehouseAnalyses.Remove(analysis);

        await _context.SaveChangesAsync();
        return true;
    }
}
