using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using KoalaWiki.Core.DataAccess;
using KoalaWiki.Domains.Warehouse;
using KoalaWiki.Functions;
using KoalaWiki.KoalaWarehouse;
using MarkAgents.Analysis.Functions;
using MarkAgents.DataAccess;
using MarkAgents.Entities;
using MarkAgents.Functions;
using MarkAgents.Git;
using MarkAgents.Infrastructure;
using MarkAgents.Options;
using MarkAgents.Prompts;
using MarkAgents.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Connectors.OpenAI;
using Repository = LibGit2Sharp.Repository;

#pragma warning disable OPENAI001

#pragma warning disable SKEXP0001
#pragma warning disable SKEXP0110

namespace MarkAgents.Analysis;

/// <summary>
/// 分析后台服务
/// </summary>
/// <param name="service"></param>
/// <param name="kernelFactory"></param>
/// <param name="logger"></param>
public sealed partial class AnalysisBackgroundService(
    IServiceProvider service,
    KernelFactory kernelFactory,
    ILogger<AnalysisBackgroundService> logger) : BackgroundService
{
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        return;
        await using var scope = service.CreateAsyncScope();

        while (stoppingToken.IsCancellationRequested == false)
        {
            try
            {
                // 每次循环都检查当前配置状态
                if (MarkOptions.EnableCodeDependencyAnalysis == false)
                {
                    logger.LogInformation("Code dependency analysis is disabled. Waiting for next check...");
                    await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
                    continue;
                }

                // 获取 DbContext
                var dbContext = scope.ServiceProvider.GetRequiredService<IKoalaWikiContext>();

                var markDbContext = scope.ServiceProvider.GetRequiredService<MarkDbContext>();

                var warehouse = await dbContext.Warehouses.FirstOrDefaultAsync(
                    x => x.Status == WarehouseStatus.Completed,
                    cancellationToken: stoppingToken);

                if (warehouse == null)
                {
                    // 如果没有仓库，等待一段时间后再次检查
                    await Task.Delay(TimeSpan.FromSeconds(10), stoppingToken);
                    continue;
                }

                // 判断是否生成过
                if (await markDbContext.WarehouseAnalyses.AnyAsync(x => x.WarehouseId == warehouse.Id,
                        cancellationToken: stoppingToken))
                {
                    // 检查仓库是否需要分析
                    if (await CheckWarehouseNeedAnalysisAsync(markDbContext, dbContext, warehouse,
                            stoppingToken))
                    {
                        // 开始分析仓库
                        await StartAnalysisWarehouseAsync(scope.ServiceProvider, warehouse);
                    }
                }
                else
                {
                    await StartAnalysisWarehouseAsync(scope.ServiceProvider, warehouse);
                }
            }
            catch (Exception ex)
            {
                // 处理异常，记录日志等
                logger.LogError(ex, "Error occurred during analysis background service execution");
            }

            // 使用配置的时间间隔，最小为10秒
            var delaySeconds = Math.Max(10, MarkOptions.AnalysisIntervalHours * 3600 / 360); // 每小时检查6次
            await Task.Delay(TimeSpan.FromSeconds(delaySeconds), stoppingToken);
        }
    }

    /// <summary>
    /// 校验仓库是否需要分析
    /// </summary>
    /// <returns></returns>
    private async Task<bool> CheckWarehouseNeedAnalysisAsync(MarkDbContext markDbContext,
        IKoalaWikiContext koalaWikiContext,
        Warehouse warehouse, CancellationToken cancellationToken)
    {
        // 找到最近的分析记录
        var lastAnalysis = await markDbContext.WarehouseAnalyses
            .Where(x => x.WarehouseId == warehouse.Id)
            .OrderByDescending(x => x.CompletedAt)
            .FirstOrDefaultAsync(cancellationToken: cancellationToken);

        // 获取document
        var documents = await koalaWikiContext.Documents
            .Where(x => x.WarehouseId == warehouse.Id)
            .FirstOrDefaultAsync(cancellationToken: cancellationToken);

        // 如果没有找到文档，无法进行分析
        if (documents?.GitPath == null)
        {
            return false;
        }

        // 如果没有历史分析记录，需要进行分析
        if (lastAnalysis == null)
        {
            return true;
        }

        if (lastAnalysis.Status is AnalysisStatus.Pending or AnalysisStatus.Running)
        {
            return true;
        }

        // 如果上次分析时间超过配置的时间间隔，则需要重新分析
        if (lastAnalysis.CreatedAt.AddHours(MarkOptions.AnalysisIntervalHours) < DateTime.UtcNow)
        {
            return true;
        }

        try
        {
            var currentCommitId =
                PullAndGetCurrentCommitId(warehouse.Address, documents.GitPath, warehouse.GitUserName,
                    warehouse.GitPassword);

            // 比较CommitId，如果不同则需要更新
            if (string.IsNullOrEmpty(currentCommitId))
            {
                // 如果获取不到当前CommitId，为了安全起见，不进行分析
                return false;
            }

            // 如果CommitId不同，说明有新的提交，需要重新分析
            return !string.Equals(lastAnalysis.CommitId, currentCommitId, StringComparison.OrdinalIgnoreCase);
        }
        catch (Exception ex)
        {
            // 如果Git操作失败，记录错误但不进行分析
            Console.WriteLine($"Error checking git commits for warehouse {warehouse.Id}: {ex.Message}");
            return false;
        }
    }

    private string PullAndGetCurrentCommitId(string warehouseAddress, string gitPath, string? gitUserName,
        string? gitPassword)
    {
        // 先判断目录是否存在
        if (!Directory.Exists(gitPath))
        {
            // 克隆下来
            GitService.CloneRepository(warehouseAddress, gitPath, gitUserName ?? "", gitPassword ?? "");
        }

        // 拉取最新代码
        GitService.PullRepository(gitPath, gitUserName ?? "", gitPassword ?? "");

        // 获取当前最新的CommitId
        using var repo = new Repository(gitPath);
        return repo.Head.Tip.Sha;
    }

    private static string GetTreePath(string path)
    {
        var ignoreFiles = DocumentsHelper.GetIgnoreFiles(path);
        var pathInfos = new List<PathInfo>();

        // 递归扫描目录所有文件和目录
        DocumentsHelper.ScanDirectory(path, pathInfos, ignoreFiles);

        var fileTree = FileTreeBuilder.BuildTree(pathInfos, path);

        return FileTreeBuilder.ToCompactString(fileTree);
    }

    /// <summary>
    /// 开始分析仓库
    /// </summary>
    private async Task StartAnalysisWarehouseAsync(IServiceProvider serviceProvider,
        Warehouse warehouse)
    {
        // 获取 DbContext
        await using var scope = serviceProvider.CreateAsyncScope();
        var markDbContext = scope.ServiceProvider.GetRequiredService<MarkDbContext>();
        var koalaWikiContext = scope.ServiceProvider.GetRequiredService<IKoalaWikiContext>();

        // 找到最近的分析记录
        var lastAnalysis = await markDbContext.WarehouseAnalyses
            .Where(x => x.WarehouseId == warehouse.Id)
            .OrderByDescending(x => x.CompletedAt)
            .FirstOrDefaultAsync();

        // 获取document
        var documents = await koalaWikiContext.Documents
            .Where(x => x.WarehouseId == warehouse.Id)
            .FirstOrDefaultAsync();

        if (documents == null)
        {
            Console.WriteLine($"No documents found for warehouse {warehouse.Id}. Skipping analysis.");
            return;
        }


        var currentCommitId =
            PullAndGetCurrentCommitId(warehouse.Address, documents.GitPath, warehouse.GitUserName,
                warehouse.GitPassword);

        WarehouseAnalysis analysis;

        if (lastAnalysis?.Status is AnalysisStatus.Pending or AnalysisStatus.Running)
        {
            // 创建新的分析记录
            analysis = lastAnalysis;
        }
        else
        {
            // 创建新的分析记录
            analysis = new WarehouseAnalysis
            {
                WarehouseId = warehouse.Id,
                CommitId = currentCommitId,
                Topic = warehouse.Name,
                Status = AnalysisStatus.Pending,
                Summary = null,
                StartCommitId = lastAnalysis?.CommitId,
                CreatedAt = DateTime.UtcNow
            };
            markDbContext.WarehouseAnalyses.Add(analysis);

            await markDbContext.SaveChangesAsync();
        }

        try
        {
            // 是否首次分析

            var kernel = kernelFactory.CreateKernel((builder =>
            {
                builder.Plugins.AddFromObject(new GiteeFunction(warehouse.OrganizationName, warehouse.Name,
                    warehouse.Branch));

                builder.Plugins.AddFromObject(new GithubFunction(warehouse.OrganizationName, warehouse.Name,
                    warehouse.Branch));

                builder.Plugins.AddFromObject(new FileFunction(documents.GitPath));
                builder.Plugins.AddFromPromptDirectory(Path.Combine(Directory.GetCurrentDirectory(), "plugins",
                    "Analysis"));


                builder.Plugins.AddFromType<AgentFunction>();
            }));

            var analysisPlugin = kernel.Plugins["Analysis"];

            if (analysisPlugin == null)
            {
                Console.WriteLine("Analysis plugin not found.");
                return;
            }

            var chatCompletionSettings = kernel.GetRequiredService<IChatCompletionService>();


            var codeFiles = GetTreePath(documents.GitPath);

            // 获取是否已经存在AgentsTask
            var existingTasks = await markDbContext.AnalysisAgentsTasks
                .Where(x => x.WarehouseId == warehouse.Id && x.WarehouseAnalysisId == analysis.Id)
                .ToListAsync();

            if (existingTasks.Count == 0)
            {
                // 开始分析
                var sb = new StringBuilder();

                var history = new ChatHistory();
                history.AddSystemPrompt();

                var contents = new ChatMessageContentItemCollection();
                contents.AddAnalysisAgentsTask(codeFiles);
                contents.AddSystemReminder();
                contents.Add(new TextContent(
                    """
                    <system-reminder> 
                    需要保证的事项：
                    最终生成格式如下：
                    <output>
                    [
                      {
                        "agentName": "SecurityAgent",
                        "name": "Analyze Authentication Security in Login Controllers",
                        "description": "Examine authentication mechanisms in login controllers and middleware. Check for secure password handling, session management, and brute force protection. Assess SQL injection risks in user queries and evaluate multi-factor authentication implementation."
                      }
                    ]
                    </output>
                    </system-reminder>
                    """));

                history.AddUserMessage(contents);

                await foreach (var item in chatCompletionSettings.GetStreamingChatMessageContentsAsync(history,
                                   new OpenAIPromptExecutionSettings()
                                   {
                                       ToolCallBehavior = ToolCallBehavior.EnableFunctions(
                                       [
                                           kernel.Plugins["AgentFunction"]["TodoWrite"].Metadata.ToOpenAIFunction()
                                       ], true)
                                   }, kernel))
                {
                    if (item is OpenAIStreamingChatMessageContent content)
                    {
                        sb.Append(content.Content);
                    }
                }

                // 正则表达式提取<output></outputs>之间的内容
                var outputRegex = OutputRegex();

                var match = outputRegex.Match(sb.ToString());

                if (match.Success)
                {
                    // 提取内容
                    var outputContent = match.Groups[1].Value.Trim();
                    sb.Clear();
                    sb.AppendLine(outputContent);
                }

                existingTasks =
                    JsonSerializer.Deserialize<List<AnalysisAgentsTask>>(sb.ToString(), JsonSerializerOptions.Web);

                existingTasks.ForEach(x =>
                {
                    x.CreatedAt = DateTime.UtcNow;
                    x.Progress = 0;
                    x.Status = AnalysisAgentsTaskStatus.NotStarted;
                    x.WarehouseId = warehouse.Id;
                    x.WarehouseAnalysisId = analysis.Id;
                });

                // 保存任务
                await markDbContext.AnalysisAgentsTasks.AddRangeAsync(existingTasks);

                await markDbContext.SaveChangesAsync();
            }

            // 检测仓库的主要编程语言
            var detectedLanguages = OptimizedPromptGenerator.DetectRepositoryLanguages(documents.GitPath);
            var primaryLanguage = detectedLanguages.FirstOrDefault() ?? "C#";

            // 智能任务分配：根据代码特征优化Agent选择
            var agentNames = existingTasks.Select(t => t.AgentName).Distinct().ToList();

            // 创建优化的Agent配置
            var generator = new OptimizedPromptGenerator();
            var agents = new Dictionary<string, string>();

            foreach (var agentName in agentNames)
            {
                var agentTasks = existingTasks.Where(t => t.AgentName == agentName).ToList();
                var primaryTask = agentTasks.FirstOrDefault();

                if (primaryTask != null)
                {
                    Console.WriteLine($"🚀 Generating optimized prompt for {agentName}...");

                    string systemPrompt = generator.GenerateSystemPrompt(new AgentContext()
                    {
                        AgentName = agentName,
                        AgentRole = DetermineAgentRole(agentName),
                        PrimaryLanguage = OptimizedPromptGenerator.ParseProgrammingLanguage(primaryLanguage),
                        Languages = detectedLanguages.Take(3)
                            .Select(OptimizedPromptGenerator.ParseProgrammingLanguage)
                            .ToList(),
                        ProjectName = primaryTask.Name,
                        AgentRequest = primaryTask.Description,
                        TaskItems = agentTasks.Select(t => $"{t.Name}: {t.Description}").ToList()
                    });

                    agents.Add(agentName, systemPrompt);
                }
            }

            foreach (var task in existingTasks.Where(x =>
                         x.Status is AnalysisAgentsTaskStatus.NotStarted or AnalysisAgentsTaskStatus.InProgress))
            {
                // 获取到当前Agents的提示词
                if (agents.TryGetValue(task.AgentName, out var dynamicInstructions))
                {
                    var result = await AgentTaskHandle.HandleAsync(kernelFactory,
                        new AgentTaskInput(codeFiles, task, dynamicInstructions, warehouse, documents));

                    try
                    {
                        // 保存任务项到数据库
                        if (result.TaskItems.Count > 0)
                        {
                            // 为每个任务项设置关联的任务ID
                            foreach (var taskItem in result.TaskItems)
                            {
                                taskItem.AnalysisAgentsTaskId = task.Id;
                                taskItem.Status = AnalysisAgentsTaskStatus.Completed;
                            }

                            // 添加到数据库
                            await markDbContext.AnalysisAgentsTaskItems.AddRangeAsync(result.TaskItems);
                            await markDbContext.SaveChangesAsync();
                        }

                        // 更新主任务状态
                        await markDbContext.AnalysisAgentsTasks
                            .Where(x => x.Id == task.Id)
                            .ExecuteUpdateAsync(x => x.SetProperty(y => y.AgentResult, result.AgentResult)
                                    .SetProperty(y => y.Status, AnalysisAgentsTaskStatus.Completed)
                                    .SetProperty(y => y.CompletedAt, DateTime.UtcNow),
                                cancellationToken: CancellationToken.None);

                        Console.WriteLine($"✅ Task {task.Id} completed: {result.TaskItems.Count} task items saved");
                    }
                    catch (Exception ex)
                    {
                        // Log the exception or handle it accordingly
                        await markDbContext.AnalysisAgentsTasks
                            .Where(x => x.Id == task.Id)
                            .ExecuteUpdateAsync(x => x.SetProperty(y => y.Status, AnalysisAgentsTaskStatus.Failed)
                                    .SetProperty(y => y.Error, ex.ToString())
                                    .SetProperty(y => y.CompletedAt, DateTime.UtcNow),
                                cancellationToken: CancellationToken.None);
                        Console.WriteLine(
                            $"❌ Error processing task {task.Id} for agent {task.AgentName}: {ex.Message}");
                    }
                }
            }

            // 根据任务状态更新分析状态
            var allTasks = await markDbContext.AnalysisAgentsTasks
                .AsNoTracking()
                .Where(x => x.WarehouseAnalysisId == analysis.Id)
                .ToListAsync(cancellationToken: CancellationToken.None);

            if (allTasks.All(x => x.Status == AnalysisAgentsTaskStatus.Completed))
            {
                // Generate comprehensive summary using TaskResultSummarizationService
                var summarizationService = scope.ServiceProvider.GetRequiredService<TaskResultSummarizationService>();
                var summaryResult = await summarizationService.SummarizeTasksAsync(analysis.Id);

                // Update analysis with completion status and summary
                await summarizationService.UpdateAnalysisStatusAsync(
                    analysis.Id,
                    AnalysisStatus.Completed,
                    summaryResult.ExecutiveSummary);

                Console.WriteLine($"✅ Analysis {analysis.Id} completed with comprehensive summary");
                Console.WriteLine(
                    $"📊 Summary: {summaryResult.TotalTaskItems} task items, {summaryResult.TotalIssuesFound} issues found");
                Console.WriteLine(
                    $"🎯 Critical Issues: {summaryResult.CriticalIssues}, High: {summaryResult.HighIssues}");
            }
            else if (allTasks.Any(x => x.Status == AnalysisAgentsTaskStatus.Failed))
            {
                await markDbContext.WarehouseAnalyses
                    .Where(x => x.Id == analysis.Id)
                    .ExecuteUpdateAsync(x => x.SetProperty(y => y.Status, AnalysisStatus.Failed)
                            .SetProperty(y => y.CompletedAt, DateTime.UtcNow),
                        cancellationToken: CancellationToken.None);

                Console.WriteLine($"❌ Analysis {analysis.Id} failed - some tasks failed");
            }
            else
            {
                // 如果有任务正在进行中，应该是出现了异常
                throw new InvalidOperationException(
                    "Some tasks are still in progress or not started, but analysis is marked as completed.");
            }
        }
        catch (Exception e)
        {
            Console.WriteLine($"❌ Error in analysis {analysis.Id}: {e}");
            await markDbContext.WarehouseAnalyses
                .Where(x => x.Id == analysis.Id)
                .ExecuteUpdateAsync(x => x.SetProperty(y => y.Status, AnalysisStatus.Failed)
                        .SetProperty(y => y.CompletedAt, DateTime.UtcNow),
                    cancellationToken: CancellationToken.None);
        }
    }


    /// <summary>
    /// Determines the role of an agent based on its name.
    /// </summary>
    private static AgentRole DetermineAgentRole(string agentName)
    {
        // 将字符串转换成美剧不区分大小写
        var normalizedAgentName = agentName.ToLowerInvariant();
        if (Enum.TryParse<AgentRole>(normalizedAgentName, true, out var role))
        {
            return role;
        }

        throw new ArgumentException($"Unknown agent role: {agentName}", nameof(agentName));
    }

    [GeneratedRegex(@"<output>(.*?)</output>", RegexOptions.Singleline)]
    private static partial Regex OutputRegex();
}