import React, { useState } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate, Link } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  ArrowLeft, 
  GitBranch, 
  Calendar, 
  Activity,
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  Search,
  Plus,
  AlertCircle,
  FileText,
  Users,
  Package,
  Shield,
  Zap,
  Layers,
  Bug,
  TestTube,
  Star,
  GitCommit,
  Settings
} from 'lucide-react';
import { useWarehouseAnalysis } from '@/hooks/use-agent-tasks';

// 代理图标映射
const agentIcons: Record<string, React.ElementType> = {
  'SecurityAgent': Shield,
  'PerformanceAgent': Zap,
  'QualityAgent': FileText,
  'ArchitectureAgent': Layers,
  'VulnerabilityAgent': Bug,
  'ComplianceAgent': Users,
  'TestingAgent': TestTube,
  'DependencyAgent': Package,
};

// 状态映射
const statusMap: Record<number, { label: string; variant: 'default' | 'secondary' | 'destructive' | 'outline'; icon: React.ElementType; color: string }> = {
  0: { label: '待处理', variant: 'outline', icon: Clock, color: 'text-gray-500' },
  1: { label: '运行中', variant: 'default', icon: Activity, color: 'text-blue-500' },
  2: { label: '已完成', variant: 'secondary', icon: CheckCircle, color: 'text-green-500' },
  3: { label: '已完成', variant: 'secondary', icon: CheckCircle, color: 'text-green-500' },
  4: { label: '失败', variant: 'destructive', icon: XCircle, color: 'text-red-500' },
};

export const RepositoryDetailView: React.FC = () => {
  const { organizationName, name } = useParams<{ organizationName: string; name: string }>();
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const { data: analyses, loading } = useWarehouseAnalysis();

  // 过滤出当前仓库的分析数据 - 通过组织名和仓库名匹配
  const repositoryAnalyses = analyses?.filter(analysis => 
    analysis.warehouseInfo?.organizationName?.toLowerCase() === organizationName?.toLowerCase() &&
    analysis.warehouseInfo?.name?.toLowerCase() === name?.toLowerCase()
  ) || [];

  // 搜索过滤
  const filteredAnalyses = repositoryAnalyses.filter(analysis =>
    searchQuery === '' || 
    analysis.topic.toLowerCase().includes(searchQuery.toLowerCase()) ||
    analysis.warehouseInfo?.name?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // 获取仓库基本信息（从第一个分析中获取）
  const repositoryInfo = repositoryAnalyses[0]?.warehouseInfo;

  // 统计信息
  const stats = {
    totalAnalyses: repositoryAnalyses.length,
    activeAnalyses: repositoryAnalyses.filter(a => a.status === 1).length,
    completedAnalyses: repositoryAnalyses.filter(a => a.status === 2 || a.status === 3).length,
    failedAnalyses: repositoryAnalyses.filter(a => a.status === 4).length,
    totalTasks: repositoryAnalyses.reduce((sum, a) => sum + (a.analysisAgentsTasks?.length || 0), 0),
    completedTasks: repositoryAnalyses.reduce((sum, a) => 
      sum + (a.analysisAgentsTasks?.filter(t => t.status === 2).length || 0), 0
    ),
  };

  // 处理分析点击
  const handleAnalysisClick = (analysis: any) => {
    navigate(`/analysis/${analysis.id}`);
  };

  // 处理任务点击
  const handleTaskClick = (analysis: any, task: any) => {
    navigate(`/tasks/${task.id}`);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="p-6">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="h-32 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
              ))}
            </div>
            <div className="space-y-4">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-64 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (repositoryAnalyses.length === 0) {
    return (
      <div className="min-h-screen bg-background">
        <div className="p-6">
          {/* Header */}
          <div className="mb-8">
            <Button variant="outline" size="sm" asChild className="mb-4">
              <Link to="/repositories">
                <ArrowLeft className="w-4 h-4 mr-2" />
                返回仓库列表
              </Link>
            </Button>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              {organizationName}/{name}
            </h1>
            <p className="text-muted-foreground mt-1">
              仓库分析详情
            </p>
          </div>

          <Card className="bg-card border-border">
            <CardContent className="flex flex-col items-center justify-center py-16">
              <GitBranch className="w-16 h-16 text-gray-300 dark:text-gray-600 mb-4" />
              <h3 className="text-xl font-medium text-foreground mb-2">
                暂无分析数据
              </h3>
              <p className="text-muted-foreground text-center mb-6">
                该仓库还没有进行过分析，开始第一次分析以查看详细信息
              </p>
              <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                <Plus className="w-4 h-4 mr-2" />
                开始分析
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <Button variant="outline" size="sm" asChild className="mb-4">
              <Link to="/repositories">
                <ArrowLeft className="w-4 h-4 mr-2" />
                返回仓库列表
              </Link>
            </Button>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              {repositoryInfo?.name || `${organizationName}/${name}` || '仓库详情'}
            </h1>
            <p className="text-muted-foreground mt-1">
              {repositoryInfo?.description || `${organizationName}/${name}`}
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
              <Plus className="w-4 h-4 mr-2" />
              新建分析
            </Button>
          </div>
        </div>

        {/* Repository Info */}
        {repositoryInfo && (
          <Card className="bg-card border-border">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <GitBranch className="w-5 h-5 text-blue-500" />
                仓库信息
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {repositoryInfo.address && (
                  <div>
                    <span className="text-sm text-muted-foreground">地址</span>
                    <p className="font-medium text-foreground truncate">
                      {repositoryInfo.address}
                    </p>
                  </div>
                )}
                {repositoryInfo.branch && (
                  <div>
                    <span className="text-sm text-muted-foreground">分支</span>
                    <p className="font-medium text-foreground">
                      {repositoryInfo.branch}
                    </p>
                  </div>
                )}
                {repositoryInfo.type && (
                  <div>
                    <span className="text-sm text-muted-foreground">类型</span>
                    <p className="font-medium text-foreground">
                      {repositoryInfo.type}
                    </p>
                  </div>
                )}
                {repositoryInfo.organizationName && (
                  <div>
                    <span className="text-sm text-muted-foreground">组织</span>
                    <p className="font-medium text-foreground">
                      {repositoryInfo.organizationName}
                    </p>
                  </div>
                )}
                {repositoryInfo.status && (
                  <div>
                    <span className="text-sm text-muted-foreground">状态</span>
                    <Badge variant="outline" className="mt-1">
                      {repositoryInfo.status}
                    </Badge>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
          <Card className="bg-card border-border">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-blue-600">{stats.totalAnalyses}</div>
              <div className="text-sm text-muted-foreground">总分析</div>
            </CardContent>
          </Card>
          <Card className="bg-card border-border">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-orange-600">{stats.activeAnalyses}</div>
              <div className="text-sm text-muted-foreground">运行中</div>
            </CardContent>
          </Card>
          <Card className="bg-card border-border">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-green-600">{stats.completedAnalyses}</div>
              <div className="text-sm text-muted-foreground">已完成</div>
            </CardContent>
          </Card>
          <Card className="bg-card border-border">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-red-600">{stats.failedAnalyses}</div>
              <div className="text-sm text-muted-foreground">失败</div>
            </CardContent>
          </Card>
          <Card className="bg-card border-border">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-purple-600">{stats.totalTasks}</div>
              <div className="text-sm text-muted-foreground">总任务</div>
            </CardContent>
          </Card>
          <Card className="bg-card border-border">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-teal-600">{stats.completedTasks}</div>
              <div className="text-sm text-muted-foreground">完成任务</div>
            </CardContent>
          </Card>
        </div>

        {/* Search */}
        <div className="relative max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            placeholder="搜索分析..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Analyses List */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold text-foreground">
            分析历史 ({filteredAnalyses.length})
          </h2>
          
          {filteredAnalyses.length === 0 ? (
            <Card className="bg-card border-border">
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Search className="w-12 h-12 text-muted-foreground mb-4" />
                <p className="text-muted-foreground">
                  没有找到匹配的分析
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {filteredAnalyses.map((analysis) => (
                <Card 
                  key={analysis.id} 
                  className="hover:shadow-lg transition-all duration-300 bg-card border-border hover:border-blue-300 dark:hover:border-blue-600"
                >
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-lg">{analysis.topic}</CardTitle>
                        <CardDescription className="flex items-center gap-4 mt-2">
                          <div className="flex items-center gap-1">
                            <Calendar className="w-4 h-4" />
                            {new Date(analysis.createdAt).toLocaleString('zh-CN')}
                          </div>
                          {analysis.completedAt && (
                            <div className="flex items-center gap-1">
                              <CheckCircle className="w-4 h-4" />
                              {new Date(analysis.completedAt).toLocaleString('zh-CN')}
                            </div>
                          )}
                        </CardDescription>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant={statusMap[analysis.status]?.variant || 'outline'}>
                          {statusMap[analysis.status]?.label || '未知'}
                        </Badge>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleAnalysisClick(analysis)}
                        >
                          <Eye className="w-4 h-4 mr-1" />
                          查看
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  
                  {analysis.analysisAgentsTasks && analysis.analysisAgentsTasks.length > 0 && (
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-muted-foreground">
                            智能代理任务 ({analysis.analysisAgentsTasks.length})
                          </span>
                          <div className="text-sm text-muted-foreground">
                            完成: {analysis.analysisAgentsTasks.filter(t => t.status === 2).length} / {analysis.analysisAgentsTasks.length}
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                          {analysis.analysisAgentsTasks.slice(0, 6).map((task: any) => {
                            const IconComponent = agentIcons[task.agentName] || Activity;
                            const taskStatus = statusMap[task.status] || { label: '未知', variant: 'outline' as const };
                            
                            return (
                              <div 
                                key={task.id} 
                                className="flex items-center gap-3 p-3 rounded-lg bg-muted hover:bg-muted/80 cursor-pointer transition-colors"
                                onClick={() => handleTaskClick(analysis, task)}
                              >
                                <IconComponent className="w-5 h-5 text-muted-foreground" />
                                <div className="flex-1 min-w-0">
                                  <div className="text-sm font-medium text-foreground truncate">
                                    {task.name || task.agentName}
                                  </div>
                                  <div className="text-xs text-muted-foreground">
                                    {task.agentName}
                                  </div>
                                </div>
                                <Badge variant={taskStatus.variant} className="text-xs">
                                  {taskStatus.label}
                                </Badge>
                              </div>
                            );
                          })}
                        </div>
                        
                        {analysis.analysisAgentsTasks.length > 6 && (
                          <div className="text-center pt-2">
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={() => handleAnalysisClick(analysis)}
                            >
                              查看全部 {analysis.analysisAgentsTasks.length} 个任务
                            </Button>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  )}
                </Card>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
